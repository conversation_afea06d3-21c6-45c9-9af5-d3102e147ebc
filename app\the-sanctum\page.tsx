import SharedLayout from "@/components/shared-layout"
import Conversion<PERSON>ackground from "@/components/conversion-background"
import TheSanctumClient from "./client"

// ISR revalidation - revalidate every 5 minutes
export const revalidate = 300

// Server-side data fetching function
async function fetchTheSanctumData() {
  try {
    // Get the base URL for API calls
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://api.loldb.info'
      : 'http://localhost:3000'

    // Fetch all required data in parallel
    const [bannersResponse, skinsResponse, chromasResponse, summonerIconsResponse, skinAugmentsResponse, emotesResponse, regaliaResponse, dropRatesResponse] = await Promise.all([
      fetch(`${baseUrl}/api/banners`, { next: { revalidate: 300 } }),
      fetch(`${baseUrl}/api/skins/all`, { next: { revalidate: 300 } }),
      fetch(`${baseUrl}/api/chromas/all`, { next: { revalidate: 300 } }),
      fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/summoner-icons.json', { next: { revalidate: 300 } }),
      fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json', { next: { revalidate: 300 } }),
      fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/summoner-emotes.json', { next: { revalidate: 300 } }),
      fetch(`${baseUrl}/api/regalia/inventory`, { next: { revalidate: 300 } }),
      fetch(`${baseUrl}/api/banners/odds`, { next: { revalidate: 300 } })
    ])

    // Parse all responses
    const bannersData = bannersResponse.ok ? await bannersResponse.json() : { data: [] }
    const skinsData = skinsResponse.ok ? await skinsResponse.json() : { data: [] }
    const chromasData = chromasResponse.ok ? await chromasResponse.json() : { data: [] }
    const summonerIconsData = summonerIconsResponse.ok ? await summonerIconsResponse.json() : []
    const skinAugmentsData = skinAugmentsResponse.ok ? await skinAugmentsResponse.json() : []
    const emotesData = emotesResponse.ok ? await emotesResponse.json() : []
    const regaliaData = regaliaResponse.ok ? await regaliaResponse.json() : { data: [] }
    const dropRatesData = dropRatesResponse.ok ? await dropRatesResponse.json() : { data: null }

    return {
      bannersData,
      skinsData,
      chromasData,
      summonerIconsData,
      skinAugmentsData,
      emotesData,
      regaliaData,
      dropRatesData
    }
  } catch (error) {
    console.error('Error fetching The Sanctum data:', error)
    return {
      bannersData: { data: [] },
      skinsData: { data: [] },
      chromasData: { data: [] },
      summonerIconsData: [],
      skinAugmentsData: [],
      emotesData: [],
      regaliaData: { data: [] },
      dropRatesData: { data: null }
    }
  }
}

export default async function TheSanctumPage() {
  // Fetch all data server-side with ISR
  const preloadedData = await fetchTheSanctumData()

  console.log('🚀 Server: Preloaded data summary:', {
    banners: preloadedData.bannersData?.data?.length || 0,
    skins: preloadedData.skinsData?.data?.length || 0,
    chromas: preloadedData.chromasData?.data?.length || 0,
    hasDropRates: !!preloadedData.dropRatesData?.data
  })

  return (
    <ConversionBackground customImageUrl="https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/seasons/images/sanctum-background.jpg">
      <SharedLayout>
        <TheSanctumClient preloadedData={preloadedData} />
      </SharedLayout>
    </ConversionBackground>
  )
}
