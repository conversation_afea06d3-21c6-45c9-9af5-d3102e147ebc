import SharedLayout from "@/components/shared-layout"
import Conversion<PERSON>ackground from "@/components/conversion-background"
import TheSanctumClient from "./client"

// ISR revalidation - revalidate every 5 minutes
export const revalidate = 300

// Server-side data fetching function
async function fetchTheSanctumData() {
  try {
    // Fetch all required data in parallel
    const [bannersResponse, skinsResponse, chromasResponse, summonerIconsResponse, skinAugmentsResponse, emotesResponse, regaliaResponse, dropRatesResponse] = await Promise.all([
      fetch('http://localhost:3000/api/banners', { next: { revalidate: 300 } }),
      fetch('http://localhost:3000/api/skins/all', { next: { revalidate: 300 } }),
      fetch('http://localhost:3000/api/chromas/all', { next: { revalidate: 300 } }),
      fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/summoner-icons.json', { next: { revalidate: 300 } }),
      fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json', { next: { revalidate: 300 } }),
      fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/summoner-emotes.json', { next: { revalidate: 300 } }),
      fetch('http://localhost:3000/api/regalia/inventory', { next: { revalidate: 300 } }),
      fetch('http://localhost:3000/api/banners/odds', { next: { revalidate: 300 } })
    ])

    // Parse all responses
    const bannersData = bannersResponse.ok ? await bannersResponse.json() : { data: [] }
    const skinsData = skinsResponse.ok ? await skinsResponse.json() : { data: [] }
    const chromasData = chromasResponse.ok ? await chromasResponse.json() : { data: [] }
    const summonerIconsData = summonerIconsResponse.ok ? await summonerIconsResponse.json() : []
    const skinAugmentsData = skinAugmentsResponse.ok ? await skinAugmentsResponse.json() : []
    const emotesData = emotesResponse.ok ? await emotesResponse.json() : []
    const regaliaData = regaliaResponse.ok ? await regaliaResponse.json() : { data: [] }
    const dropRatesData = dropRatesResponse.ok ? await dropRatesResponse.json() : { data: null }

    return {
      bannersData,
      skinsData,
      chromasData,
      summonerIconsData,
      skinAugmentsData,
      emotesData,
      regaliaData,
      dropRatesData
    }
  } catch (error) {
    console.error('Error fetching The Sanctum data:', error)
    return {
      bannersData: { data: [] },
      skinsData: { data: [] },
      chromasData: { data: [] },
      summonerIconsData: [],
      skinAugmentsData: [],
      emotesData: [],
      regaliaData: { data: [] },
      dropRatesData: { data: null }
    }
  }
}

export default async function TheSanctumPage() {
  // Fetch all data server-side with ISR
  const preloadedData = await fetchTheSanctumData()

  return (
    <SharedLayout>
      <ConversionBackground />
      <TheSanctumClient preloadedData={preloadedData} />
    </SharedLayout>
  )
}
