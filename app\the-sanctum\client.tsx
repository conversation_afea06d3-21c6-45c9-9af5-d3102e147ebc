"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Clock } from "lucide-react"
import Image from "next/image"
import { SkinRarityService } from "@/lib/api/skin-rarity-service"
import { createChromaURL } from "@/lib/utils/chroma-url-utils"

interface BannerSkin {
  id: number
  name: string
  rarity: string
}

interface BannerData {
  id: string
  bannerBackgroundTexture: string
  bannerChaseAnimationWebmPath: string
  bannerChaseAnimationParallax: string
  bannerSkin: BannerSkin
  startDate: number
  endDate: number
  rollVignetteSkinIntroSfxPath?: string
  rollVignetteSkinIntroWebmPath?: string
}

interface ProcessedSkinData {
  id: number
  name: string
  champion: string
  championKey: string
  tier: string
  rarity: string
  price: string
  image: string
  splashArt: string
  videoUrl?: string
  isLegacy: boolean
  isBase: boolean
  skinType: string
  contentId: string
  skinNumber: number
  skinLines: string[]
  description?: string
  featuresText?: string
  hasNewEffects: boolean | null
  hasNewAnimations: boolean | null
  hasNewRecall: boolean | null
  hasChromas: boolean
  set?: string
  releaseDate?: string
  availability: string | null
  lootEligible: boolean | null
  lastDiscount?: string
  itemId?: number
  questSkinInfo?: any
  newVoiceLines?: boolean | null
  isUpcoming?: boolean
  releaseCountdown?: string
}

interface PreloadedData {
  bannersData: { data: BannerData[] }
  skinsData: { data: ProcessedSkinData[] }
  chromasData: { data: any[] }
  summonerIconsData: any[]
  skinAugmentsData: any[]
  emotesData: any[]
  regaliaData: { data: any[] }
  dropRatesData: { data: any }
}

interface TheSanctumClientProps {
  preloadedData: PreloadedData
}

// Helper function to extract path after /ASSETS/ and convert to Community Dragon URL
const extractAssetPath = (fullPath: string): string => {
  const assetsIndex = fullPath.indexOf('/ASSETS/')
  if (assetsIndex === -1) return ''
  
  const pathAfterAssets = fullPath.substring(assetsIndex)
  return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${pathAfterAssets.toLowerCase()}`
}

// Helper function to convert skin name to display name
const formatSkinName = (skinName: string): string => {
  // Remove "Skin" prefix and numbers, then format
  const cleaned = skinName.replace(/Skin\d+$/, '').replace(/([A-Z])/g, ' $1').trim()
  return cleaned.charAt(0).toUpperCase() + cleaned.slice(1)
}

// Helper function to create URL-friendly skin slug (same as in /skins pages)
const createSkinSlug = (skinName: string): string => {
  return skinName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

// Helper function to handle skin click navigation
const handleSkinClick = (skinData: any, router: any) => {
  if (skinData?.name) {
    const skinSlug = createSkinSlug(skinData.name)
    router.push(`/skins/${skinSlug}`)
  }
}

// Helper function to handle chroma click navigation
const handleChromaClick = (chromaData: any, router: any) => {
  if (chromaData) {
    try {
      const chromaUrl = createChromaURL(chromaData)
      router.push(`/skins/chromas/${chromaUrl}`)
    } catch (error) {
      console.error('Error creating chroma URL:', error)
    }
  }
}

// Helper function to get rarity icon URL using SkinRarityService
const getRarityIconUrl = (rarity: string): string => {
  return SkinRarityService.getRarityIconUrl(rarity) || ''
}

// Helper function to format countdown
const formatCountdown = (endDate: number): string => {
  // Convert Unix timestamp (seconds) to milliseconds
  const end = new Date(endDate * 1000)
  const now = new Date()
  const diff = end.getTime() - now.getTime()

  if (diff <= 0) return 'Ended'

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) return `${days}d ${hours}h ${minutes}m`
  if (hours > 0) return `${hours}h ${minutes}m`
  return `${minutes}m`
}

export default function TheSanctumClient({ preloadedData }: TheSanctumClientProps) {
  const router = useRouter()
  const [allBanners, setAllBanners] = useState<BannerData[]>([])
  const [selectedBanner, setSelectedBanner] = useState<BannerData | null>(null)
  const [skinData, setSkinData] = useState<ProcessedSkinData | null>(null)
  const [allSkinsData, setAllSkinsData] = useState<ProcessedSkinData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [countdown, setCountdown] = useState('')
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [previewVideoUrl, setPreviewVideoUrl] = useState<string>('')
  const [previewAudioUrl, setPreviewAudioUrl] = useState<string>('')
  const [isDropRatesMode, setIsDropRatesMode] = useState(false)
  const [dropRatesData, setDropRatesData] = useState<any>(null)
  const [enrichedDropRatesData, setEnrichedDropRatesData] = useState<any>(null)
  const [currentSkinId, setCurrentSkinId] = useState<number | null>(null)

  // Initialize with preloaded data
  useEffect(() => {
    if (preloadedData) {
      console.log('🔄 Initializing with preloaded data:', preloadedData)

      const banners = preloadedData.bannersData?.data || []
      const skins = preloadedData.skinsData?.data || []

      console.log('📊 Banners:', banners.length)
      console.log('🎨 Skins:', skins.length)

      setAllBanners(banners)
      setAllSkinsData(skins)
      setLoading(false)

      // Set the first banner as selected if available
      if (banners.length > 0) {
        console.log('🎯 Setting first banner:', banners[0])
        setSelectedBanner(banners[0])
      } else {
        console.log('❌ No banners available')
        setError('No banner data available')
      }
    } else {
      console.log('❌ No preloaded data received')
      setError('Failed to load data')
      setLoading(false)
    }
  }, [preloadedData])

  // Enrich drop rates data with skin/chroma images
  const enrichDropRatesData = async (dropRatesData: any) => {
    try {
      // Use preloaded data instead of fetching
      const allSkins = preloadedData.skinsData.data || []
      const allChromas = preloadedData.chromasData.data || []
      const summonerIconsData = preloadedData.summonerIconsData || []
      const skinAugmentsData = preloadedData.skinAugmentsData || []
      const emotesData = preloadedData.emotesData || []
      const regaliaData = preloadedData.regaliaData || { data: [] }

      // Build summoner icons map
      const summonerIconsMap = new Map()
      if (Array.isArray(summonerIconsData)) {
        summonerIconsData.forEach((icon: any) => {
          if (icon.id && icon.iconPath) {
            const pathMatch = icon.iconPath.match(/\/ASSETS\/(.+)/)
            if (pathMatch) {
              const extractedPath = pathMatch[1].toLowerCase()
              summonerIconsMap.set(icon.id, {
                name: icon.name || `Summoner Icon ${icon.id}`,
                image: `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${extractedPath}`
              })
            }
          }
        })
      }

      // Build skin augments map
      const skinAugmentsMap = new Map()
      if (skinAugmentsData && typeof skinAugmentsData === 'object') {
        Object.values(skinAugmentsData).forEach((skin: any) => {
          if (skin.skinAugments && skin.skinAugments.borders) {
            Object.values(skin.skinAugments.borders).forEach((layerArray: any) => {
              if (Array.isArray(layerArray)) {
                layerArray.forEach((border: any) => {
                  if (border.contentId && border.borderPath) {
                    const pathMatch = border.borderPath.match(/\/ASSETS\/(.+)/)
                    if (pathMatch) {
                      const extractedPath = pathMatch[1].toLowerCase()
                      const finalUrl = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${extractedPath}`
                      skinAugmentsMap.set(border.contentId, {
                        name: `${skin.name} Border` || `Skin Augment ${border.contentId}`,
                        image: finalUrl
                      })
                    }
                  }
                })
              }
            })
          }
        })
      }

      // Build emotes map
      const emotesMap = new Map()
      if (Array.isArray(emotesData)) {
        emotesData.forEach((emote: any) => {
          if (emote.id && emote.inventoryIcon) {
            const pathMatch = emote.inventoryIcon.match(/\/ASSETS\/(.+)/)
            if (pathMatch) {
              const extractedPath = pathMatch[1].toLowerCase()
              const finalUrl = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${extractedPath}`
              emotesMap.set(emote.id, {
                name: emote.name || `Emote ${emote.id}`,
                image: finalUrl
              })
            }
          }
        })
      }

      // Build regalia map
      const regaliaMap = new Map()
      if (regaliaData.data && typeof regaliaData.data === 'object') {
        Object.entries(regaliaData.data).forEach(([, regaliaEntry]: [string, any]) => {
          if (regaliaEntry.items && Array.isArray(regaliaEntry.items)) {
            regaliaEntry.items.forEach((item: any) => {
              if (item.id && item.assetPath) {
                const numericId = parseInt(item.id, 10)
                const pathMatch = item.assetPath.match(/\/ASSETS\/(.+)/)
                if (pathMatch) {
                  const extractedPath = pathMatch[1].toLowerCase()
                  const finalUrl = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${extractedPath}`
                  regaliaMap.set(numericId, {
                    name: item.localizedName || `Regalia Banner ${item.id}`,
                    image: finalUrl
                  })
                }
              }
            })
          }
        })
      }

      // Helper function to enrich a child item
      const enrichChild = (child: any) => {
        let enrichedChild = { ...child }

        if (child.type === 'CHAMPION_SKIN') {
          const matchingSkin = allSkins.find((skin: any) => skin.id === child.id)
          if (matchingSkin) {
            enrichedChild.skinData = {
              name: matchingSkin.name,
              image: matchingSkin.image
            }
          }
        } else if (child.type === 'CHAMPION_SKIN_CHROMA') {
          const matchingChroma = allChromas.find((chroma: any) => chroma.id === child.id)
          if (matchingChroma) {
            enrichedChild.chromaData = {
              name: matchingChroma.name,
              image: matchingChroma.image,
              skinName: matchingChroma.skinName,
              color: matchingChroma.color,
              colors: matchingChroma.colors
            }
          }
        } else if (child.type === 'SUMMONER_ICON') {
          const matchingIcon = summonerIconsMap.get(child.id)
          if (matchingIcon) {
            enrichedChild.summonerIconData = {
              name: matchingIcon.name,
              image: matchingIcon.image
            }
          }
        } else if (child.type === 'SKIN_AUGMENT') {
          const matchingAugment = skinAugmentsMap.get(child.itemInstanceId)
          if (matchingAugment) {
            enrichedChild.skinAugmentData = {
              name: matchingAugment.name,
              image: matchingAugment.image
            }
          }
        } else if (child.type === 'EMOTE') {
          const matchingEmote = emotesMap.get(child.id)
          if (matchingEmote) {
            enrichedChild.emoteData = {
              name: matchingEmote.name,
              image: matchingEmote.image
            }
          }
        } else if (child.type === 'REGALIA_BANNER') {
          const matchingRegalia = regaliaMap.get(child.id)
          if (matchingRegalia) {
            enrichedChild.regaliaData = {
              name: matchingRegalia.name,
              image: matchingRegalia.image
            }
          }
        }

        return enrichedChild
      }

      // Enrich each reward table
      const enrichedRewardTables = dropRatesData.rewardTables?.map((table: any) => ({
        ...table,
        children: table.children?.map(enrichChild),
        fallbackChildren: table.fallbackChildren?.map(enrichChild)
      }))

      return {
        ...dropRatesData,
        rewardTables: enrichedRewardTables
      }
    } catch (error) {
      console.error('Failed to enrich drop rates data:', error)
      return dropRatesData
    }
  }

  // Handle banner selection and skin data loading
  useEffect(() => {
    if (selectedBanner && allSkinsData.length > 0) {
      console.log('🔍 Looking for skin with ID:', selectedBanner.bannerSkin?.id)
      console.log('📋 Available skins:', allSkinsData.map(s => ({ id: s.id, name: s.name })))

      const matchingSkin = allSkinsData.find(skin => skin.id === selectedBanner.bannerSkin?.id)
      if (matchingSkin) {
        console.log('✅ Found matching skin:', matchingSkin.name)
        setSkinData(matchingSkin)
        setCurrentSkinId(matchingSkin.id)
      } else {
        console.log('❌ No matching skin found for banner skin ID:', selectedBanner.bannerSkin?.id)
        // Try to use the first skin as fallback
        if (allSkinsData.length > 0) {
          console.log('🔄 Using first available skin as fallback')
          setSkinData(allSkinsData[0])
          setCurrentSkinId(allSkinsData[0].id)
        }
      }
    }
  }, [selectedBanner, allSkinsData])

  // Handle drop rates loading
  useEffect(() => {
    if (currentSkinId && preloadedData.dropRatesData.data) {
      const matchingDropRates = preloadedData.dropRatesData.data.rewardTables?.find((table: any) =>
        table.children?.some((child: any) => child.id === currentSkinId)
      )

      if (matchingDropRates) {
        const enrichedData = enrichDropRatesData(preloadedData.dropRatesData.data)
        setDropRatesData(preloadedData.dropRatesData.data)
        setEnrichedDropRatesData(enrichedData)
        setIsDropRatesMode(true)
      }
    }
  }, [currentSkinId, preloadedData.dropRatesData])

  // Countdown timer
  useEffect(() => {
    if (!selectedBanner) return

    const updateCountdown = () => {
      setCountdown(formatCountdown(selectedBanner.endDate))
    }

    updateCountdown()
    const interval = setInterval(updateCountdown, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [selectedBanner])

  // Handle preview mode
  const handlePreviewClick = () => {
    if (selectedBanner) {
      const videoPath = selectedBanner.rollVignetteSkinIntroWebmPath
      const audioPath = selectedBanner.rollVignetteSkinIntroSfxPath

      if (videoPath) {
        setPreviewVideoUrl(extractAssetPath(videoPath))
      }
      if (audioPath) {
        setPreviewAudioUrl(extractAssetPath(audioPath))
      }
      setIsPreviewMode(true)
    }
  }

  const handleBackClick = () => {
    setIsDropRatesMode(false)
    setIsPreviewMode(false)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-white text-xl">Loading The Sanctum...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-500 text-xl">{error}</div>
      </div>
    )
  }

  if (!selectedBanner || !skinData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-white text-xl">No banner data available</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between p-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-3xl font-bold text-white">The Sanctum</h1>
            {countdown && (
              <div className="flex items-center space-x-2 bg-black/30 rounded-lg px-3 py-1">
                <Clock className="h-4 w-4 text-white" />
                <span className="text-white text-sm font-medium">{countdown}</span>
              </div>
            )}
          </div>

          {/* Banner selector */}
          {allBanners.length > 1 && (
            <div className="flex space-x-2">
              {allBanners.map((banner, index) => (
                <button
                  key={banner.id}
                  onClick={() => setSelectedBanner(banner)}
                  className={`w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                    selectedBanner.id === banner.id
                      ? 'border-yellow-400 scale-110'
                      : 'border-gray-600 hover:border-gray-400'
                  }`}
                >
                  <Image
                    src={extractAssetPath(banner.bannerBackgroundTexture)}
                    alt={`Banner ${index + 1}`}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Main content */}
        <div className="flex-1 flex items-center justify-center p-6">
          {isPreviewMode ? (
            // Preview mode content
            <div className="text-center">
              <button
                onClick={handleBackClick}
                className="mb-4 text-white hover:text-yellow-400 transition-colors"
              >
                ← Back to Banner
              </button>
              {previewVideoUrl && (
                <video
                  src={previewVideoUrl}
                  autoPlay
                  loop
                  muted
                  className="max-w-2xl w-full rounded-lg"
                />
              )}
            </div>
          ) : isDropRatesMode && enrichedDropRatesData ? (
            // Drop rates mode content will be added in next step
            <div className="text-white text-center">
              <button
                onClick={handleBackClick}
                className="mb-4 text-white hover:text-yellow-400 transition-colors"
              >
                ← Back to Banner
              </button>
              <div>Drop Rates Display (Implementation in progress...)</div>
            </div>
          ) : (
            // Main banner display
            <div className="text-center max-w-4xl">
              {/* Skin image */}
              <div className="mb-8">
                <Image
                  src={skinData.splashArt || skinData.image}
                  alt={skinData.name}
                  width={800}
                  height={450}
                  className="rounded-lg shadow-2xl mx-auto"
                  priority
                />
              </div>

              {/* Skin info */}
              <div className="mb-8">
                <h2 className="text-4xl font-bold text-white mb-2">{skinData.name}</h2>
                <p className="text-xl text-gray-300 mb-4">{skinData.champion}</p>

                {/* Rarity and price */}
                <div className="flex items-center justify-center space-x-6 mb-6">
                  <div className="flex items-center space-x-2">
                    <Image
                      src={getRarityIconUrl(skinData.rarity)}
                      alt={skinData.rarity}
                      width={24}
                      height={24}
                    />
                    <span className="text-white font-medium">{skinData.rarity}</span>
                  </div>
                  <div className="text-yellow-400 font-bold text-lg">
                    {skinData.price}
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex justify-center space-x-4">
                {selectedBanner.rollVignetteSkinIntroWebmPath && (
                  <button
                    onClick={handlePreviewClick}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                  >
                    Preview Animation
                  </button>
                )}

                {isDropRatesMode && (
                  <button
                    onClick={() => setIsDropRatesMode(true)}
                    className="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                  >
                    View Drop Rates
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
    </div>
  )
}
